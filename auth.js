// Authentication and User Management System
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.users = this.loadUsers();
        this.userSessions = JSON.parse(localStorage.getItem('userSessions')) || {};
        this.activityLog = JSON.parse(localStorage.getItem('activityLog')) || [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.applyTheme();
        this.checkExistingSession();
        this.initializeDefaultUsers();
    }

    // Initialize default users if none exist
    initializeDefaultUsers() {
        if (Object.keys(this.users).length === 0) {
            this.users = {
                'admin': {
                    id: 'admin',
                    username: 'admin',
                    password: this.hashPassword('admin123'),
                    fullName: 'مدير النظام',
                    email: '<EMAIL>',
                    role: 'admin',
                    permissions: ['read', 'write', 'delete', 'manage_users', 'view_analytics', 'manage_system'],
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                },
                'analyst': {
                    id: 'analyst',
                    username: 'analyst',
                    password: this.hashPassword('analyst123'),
                    fullName: 'محلل أمني',
                    email: '<EMAIL>',
                    role: 'analyst',
                    permissions: ['read', 'write', 'view_analytics'],
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                },
                'operator': {
                    id: 'operator',
                    username: 'operator',
                    password: this.hashPassword('operator123'),
                    fullName: 'مشغل النظام',
                    email: '<EMAIL>',
                    role: 'operator',
                    permissions: ['read'],
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                }
            };
            this.saveUsers();
        }
    }

    setupEventListeners() {
        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // Password toggle
        const passwordToggle = document.getElementById('passwordToggle');
        if (passwordToggle) {
            passwordToggle.addEventListener('click', () => {
                this.togglePasswordVisibility();
            });
        }

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Logout functionality
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.logout();
            });
        }
    }

    // Simple password hashing (in production, use proper hashing)
    hashPassword(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    // Handle login
    async handleLogin() {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        if (!username || !password) {
            this.showError('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }

        this.showLoading(true);

        // Simulate API call delay
        setTimeout(() => {
            const user = this.authenticateUser(username, password);
            
            if (user) {
                this.loginSuccess(user, rememberMe);
            } else {
                this.showError('اسم المستخدم أو كلمة المرور غير صحيح');
                this.showLoading(false);
            }
        }, 1000);
    }

    // Authenticate user
    authenticateUser(username, password) {
        const user = this.users[username];
        
        if (!user) {
            return null;
        }

        if (!user.isActive) {
            this.showError('هذا الحساب معطل');
            return null;
        }

        const hashedPassword = this.hashPassword(password);
        if (user.password === hashedPassword) {
            return user;
        }

        return null;
    }

    // Login success
    loginSuccess(user, rememberMe) {
        this.currentUser = user;
        
        // Update last login
        user.lastLogin = new Date().toISOString();
        this.users[user.username] = user;
        this.saveUsers();

        // Create session
        const sessionId = this.generateSessionId();
        const session = {
            userId: user.id,
            username: user.username,
            role: user.role,
            permissions: user.permissions,
            createdAt: new Date().toISOString(),
            expiresAt: rememberMe ? 
                new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() : // 30 days
                new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString() // 8 hours
        };

        this.userSessions[sessionId] = session;
        localStorage.setItem('userSessions', JSON.stringify(this.userSessions));
        localStorage.setItem('currentSession', sessionId);

        // Log activity
        this.logActivity('login', `تسجيل دخول المستخدم ${user.fullName}`);

        // Redirect to main application
        window.location.href = 'index.html';
    }

    // Generate session ID
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Check existing session
    checkExistingSession() {
        const sessionId = localStorage.getItem('currentSession');
        if (!sessionId) return false;

        const session = this.userSessions[sessionId];
        if (!session) return false;

        // Check if session is expired
        if (new Date() > new Date(session.expiresAt)) {
            this.clearSession(sessionId);
            return false;
        }

        // Session is valid
        this.currentUser = this.users[session.username];
        return true;
    }

    // Clear session
    clearSession(sessionId) {
        delete this.userSessions[sessionId];
        localStorage.setItem('userSessions', JSON.stringify(this.userSessions));
        localStorage.removeItem('currentSession');
    }

    // Logout
    logout() {
        if (this.currentUser) {
            this.logActivity('logout', `تسجيل خروج المستخدم ${this.currentUser.fullName}`);
        }

        const sessionId = localStorage.getItem('currentSession');
        if (sessionId) {
            this.clearSession(sessionId);
        }

        this.currentUser = null;
        window.location.href = 'login.html';
    }

    // Check if user has permission
    hasPermission(permission) {
        if (!this.currentUser) return false;
        return this.currentUser.permissions.includes(permission);
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Show loading state
    showLoading(show) {
        const loginBtn = document.getElementById('loginBtn');
        const spinner = loginBtn.querySelector('.loading-spinner');
        const text = loginBtn.querySelector('span');

        if (show) {
            loginBtn.disabled = true;
            spinner.style.display = 'inline-block';
            text.textContent = 'جاري التحقق...';
        } else {
            loginBtn.disabled = false;
            spinner.style.display = 'none';
            text.textContent = 'تسجيل الدخول';
        }
    }

    // Show error message
    showError(message) {
        const errorDiv = document.getElementById('loginError');
        const errorMessage = document.getElementById('errorMessage');
        
        errorMessage.textContent = message;
        errorDiv.style.display = 'flex';
        
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }

    // Toggle password visibility
    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('#passwordToggle i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    }

    // Theme management
    toggleTheme() {
        const currentTheme = localStorage.getItem('theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
        localStorage.setItem('theme', newTheme);
    }

    applyTheme(theme = null) {
        const currentTheme = theme || localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', currentTheme);
        
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // Activity logging
    logActivity(action, description) {
        const activity = {
            id: Date.now(),
            userId: this.currentUser ? this.currentUser.id : 'anonymous',
            username: this.currentUser ? this.currentUser.username : 'anonymous',
            action: action,
            description: description,
            timestamp: new Date().toISOString(),
            ip: 'localhost', // In production, get real IP
            userAgent: navigator.userAgent
        };

        this.activityLog.unshift(activity);
        
        // Keep only last 1000 activities
        if (this.activityLog.length > 1000) {
            this.activityLog = this.activityLog.slice(0, 1000);
        }

        localStorage.setItem('activityLog', JSON.stringify(this.activityLog));
    }

    // Load users from storage
    loadUsers() {
        return JSON.parse(localStorage.getItem('systemUsers')) || {};
    }

    // Save users to storage
    saveUsers() {
        localStorage.setItem('systemUsers', JSON.stringify(this.users));
    }
}

// Fill demo account credentials
function fillDemoAccount(username, password) {
    document.getElementById('username').value = username;
    document.getElementById('password').value = password;
}

// Initialize auth manager
const authManager = new AuthManager();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
