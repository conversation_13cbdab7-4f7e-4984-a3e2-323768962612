// Security Events Management System
class SecurityEventsManager {
    constructor() {
        try {
            this.events = JSON.parse(localStorage.getItem('securityEvents')) || [];
            this.eventTypes = JSON.parse(localStorage.getItem('eventTypes')) || [];
            this.currentEventId = parseInt(localStorage.getItem('currentEventId')) || 1;
            this.currentTheme = localStorage.getItem('theme') || 'light';
            this.authManager = null;
            this.currentUser = null;
            this.currentEditingEventTypeKey = null;

            this.initializeDefaultEventTypes();
            this.init();
        } catch (error) {
            console.error('Error initializing SecurityEventsManager:', error);
            // Reset to defaults if localStorage is corrupted
            this.events = [];
            this.eventTypes = [];
            this.currentEventId = 1;
            this.currentTheme = 'light';
            this.currentUser = null;
            this.initializeDefaultEventTypes();
            this.init();
        }
    }

    // Initialize default event types if none exist
    initializeDefaultEventTypes() {
        if (this.eventTypes.length === 0) {
            this.eventTypes = [
                {
                    key: 'malware',
                    label: 'برمجية خبيثة',
                    description: 'فيروسات، أحصنة طروادة، برامج التجسس',
                    icon: 'fas fa-virus',
                    color: '#dc2626'
                },
                {
                    key: 'phishing',
                    label: 'تصيد إلكتروني',
                    description: 'محاولات سرقة البيانات عبر رسائل مزيفة',
                    icon: 'fas fa-fishing',
                    color: '#ea580c'
                },
                {
                    key: 'intrusion',
                    label: 'اختراق',
                    description: 'محاولات الوصول غير المصرح به للأنظمة',
                    icon: 'fas fa-user-secret',
                    color: '#7c3aed'
                },
                {
                    key: 'data-breach',
                    label: 'تسريب بيانات',
                    description: 'تسريب أو فقدان البيانات الحساسة',
                    icon: 'fas fa-database',
                    color: '#dc2626'
                },
                {
                    key: 'unauthorized-access',
                    label: 'وصول غير مصرح',
                    description: 'محاولات الوصول بدون تصريح',
                    icon: 'fas fa-shield-alt',
                    color: '#d97706'
                },
                {
                    key: 'system-failure',
                    label: 'فشل النظام',
                    description: 'أعطال في الأنظمة الأمنية',
                    icon: 'fas fa-server',
                    color: '#6b7280'
                }
            ];
            this.saveEventTypesToStorage();
        }
    }

    init() {
        // Check authentication first
        if (!this.checkAuthentication()) {
            // Add a small delay to avoid redirect loops
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 100);
            return;
        }

        this.setupEventListeners();
        this.applyTheme();
        this.setupUserInterface();
        this.setupLogo();
        this.updateEventTypeDropdown();
        this.renderEvents();
        this.setCurrentDateTime();
        this.updateStatistics();
        this.initializeCharts();
        this.updateRiskAnalysis();
        this.initPostIncidentReview();
    }

    // Check if user is authenticated
    checkAuthentication() {
        // Skip authentication check if we're on login page
        if (window.location.pathname.includes('login.html')) {
            return true;
        }

        const sessionId = localStorage.getItem('currentSession');
        if (!sessionId) return false;

        const userSessions = JSON.parse(localStorage.getItem('userSessions')) || {};
        const session = userSessions[sessionId];

        if (!session) return false;

        // Check if session is expired
        if (new Date() > new Date(session.expiresAt)) {
            this.clearSession(sessionId);
            return false;
        }

        // Load current user
        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
        this.currentUser = users[session.username];

        return this.currentUser && this.currentUser.isActive;
    }

    // Clear expired session
    clearSession(sessionId) {
        const userSessions = JSON.parse(localStorage.getItem('userSessions')) || {};
        delete userSessions[sessionId];
        localStorage.setItem('userSessions', JSON.stringify(userSessions));
        localStorage.removeItem('currentSession');
    }

    // Setup user interface based on permissions
    setupUserInterface() {
        if (!this.currentUser) {
            console.error('No current user found');
            return;
        }

        // Update user name in header
        const userNameElement = document.getElementById('currentUserName');
        if (userNameElement) {
            userNameElement.textContent = this.currentUser.fullName;
        }

        // Show/hide sections based on permissions
        this.updateUIPermissions();
    }

    // Update UI based on user permissions
    updateUIPermissions() {
        const user = this.currentUser;
        if (!user) return;

        // Show admin-only sections
        if (user.permissions.includes('manage_users')) {
            document.querySelectorAll('.admin-only').forEach(el => {
                el.style.display = 'flex';
            });
        } else {
            document.querySelectorAll('.admin-only').forEach(el => {
                el.style.display = 'none';
            });
        }

        // Disable write operations if user doesn't have write permission
        if (!user.permissions.includes('write')) {
            const eventForm = document.getElementById('eventForm');
            if (eventForm) {
                eventForm.style.display = 'none';
            }
        }

        // Hide edit/delete buttons if user doesn't have permissions
        setTimeout(() => {
            if (!user.permissions.includes('write')) {
                document.querySelectorAll('.edit-btn').forEach(btn => {
                    btn.style.display = 'none';
                });
            }
            if (!user.permissions.includes('delete')) {
                document.querySelectorAll('.delete-btn').forEach(btn => {
                    btn.style.display = 'none';
                });
            }
        }, 100);

        // Hide analytics if user doesn't have permission
        if (!user.permissions.includes('view_analytics')) {
            const analyticsBtn = document.querySelector('[data-section="analytics"]');
            const riskBtn = document.querySelector('[data-section="risk-analysis"]');
            if (analyticsBtn) analyticsBtn.style.display = 'none';
            if (riskBtn) riskBtn.style.display = 'none';
        }
    }

    // Setup logo with fallback
    setupLogo() {
        const logoImages = document.querySelectorAll('.logo-image');
        logoImages.forEach(img => {
            img.addEventListener('error', function() {
                // Hide the image and show the icon fallback
                this.style.display = 'none';
                const icon = this.nextElementSibling;
                if (icon && icon.tagName === 'I') {
                    icon.style.display = 'inline-block';
                }
            });

            img.addEventListener('load', function() {
                // Hide the icon fallback when image loads successfully
                const icon = this.nextElementSibling;
                if (icon && icon.tagName === 'I') {
                    icon.style.display = 'none';
                }
            });
        });
    }

    setupEventListeners() {
        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        // Event form submission
        document.getElementById('eventForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addEvent();
        });

        // Search and filter
        document.getElementById('searchEvents').addEventListener('input', (e) => {
            this.filterEvents();
        });

        document.getElementById('filterSeverity').addEventListener('change', (e) => {
            this.filterEvents();
        });

        // Modal controls
        document.getElementById('modalClose').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('closeModalBtn').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('deleteEventBtn').addEventListener('click', () => {
            this.deleteCurrentEvent();
        });

        // Close modal when clicking outside
        document.getElementById('eventModal').addEventListener('click', (e) => {
            if (e.target.id === 'eventModal') {
                this.closeModal();
            }
        });

        // Navigation buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchSection(e.target.closest('.nav-btn').dataset.section);
            });
        });

        // User management
        const addUserBtn = document.getElementById('addUserBtn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => {
                this.showUserModal();
            });
        }

        const userModalClose = document.getElementById('userModalClose');
        if (userModalClose) {
            userModalClose.addEventListener('click', () => {
                this.closeUserModal();
            });
        }

        const cancelUserBtn = document.getElementById('cancelUserBtn');
        if (cancelUserBtn) {
            cancelUserBtn.addEventListener('click', () => {
                this.closeUserModal();
            });
        }

        const saveUserBtn = document.getElementById('saveUserBtn');
        if (saveUserBtn) {
            saveUserBtn.addEventListener('click', () => {
                this.saveUser();
            });
        }

        // User menu dropdown
        const userMenu = document.getElementById('userMenu');
        if (userMenu) {
            userMenu.addEventListener('click', () => {
                userMenu.classList.toggle('active');
            });
        }

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.logout();
            });
        }

        // Role change handler
        const userRole = document.getElementById('userRole');
        if (userRole) {
            userRole.addEventListener('change', () => {
                this.updatePermissionsByRole();
            });
        }

        // Event Types Management
        const addEventTypeBtn = document.getElementById('addEventTypeBtn');
        if (addEventTypeBtn) {
            addEventTypeBtn.addEventListener('click', () => {
                this.showEventTypeModal();
            });
        }
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        localStorage.setItem('theme', this.currentTheme);
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        const themeIcon = document.querySelector('#themeToggle i');
        themeIcon.className = this.currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
    }

    setCurrentDateTime() {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
            .toISOString().slice(0, 16);
        document.getElementById('eventDate').value = localDateTime;
    }



    addEvent() {
        // Check if user has write permission
        if (!this.currentUser || !this.currentUser.permissions.includes('write')) {
            this.showNotification('ليس لديك صلاحية لإضافة الأحداث', 'error');
            return;
        }

        const formData = new FormData(document.getElementById('eventForm'));

        // Validate serial number
        const serialNumber = formData.get('eventSerial');
        if (!serialNumber || !serialNumber.trim()) {
            this.showAlert('يرجى إدخال رقم التسلسل', 'error');
            return;
        }

        // Check if serial number already exists
        if (this.events.some(event => event.serialNumber === serialNumber.trim())) {
            this.showAlert('رقم التسلسل موجود بالفعل، يرجى استخدام رقم مختلف', 'error');
            return;
        }

        const event = {
            id: this.currentEventId++,
            serialNumber: serialNumber.trim(),
            title: formData.get('eventTitle'),
            severity: formData.get('eventSeverity'),
            type: formData.get('eventType'),
            date: formData.get('eventDate'),
            description: formData.get('eventDescription'),
            detailedDescription: formData.get('eventDetailedDescription') || '',
            affectedSystems: formData.get('affectedSystems'),
            responsiblePerson: formData.get('responsiblePerson'),
            directCosts: formData.get('directCosts') || '',
            indirectCosts: formData.get('indirectCosts') || '',
            status: 'open',
            createdAt: new Date().toISOString(),
            createdBy: this.currentUser.username
        };

        this.events.unshift(event);
        this.saveToStorage();
        this.renderEvents();
        this.updateStatistics();
        this.updateCharts();
        this.updateRiskAnalysis();
        this.logActivity('event_create', `إضافة حدث أمني: ${event.title}`);
        this.showNotification('تم حفظ الحدث بنجاح', 'success');

        // Reset form
        document.getElementById('eventForm').reset();
        this.setCurrentDateTime();
    }

    renderEvents() {
        const tbody = document.getElementById('eventsTableBody');
        const searchTerm = document.getElementById('searchEvents').value.toLowerCase();
        const severityFilter = document.getElementById('filterSeverity').value;

        let filteredEvents = this.events;

        // Apply filters
        if (searchTerm) {
            filteredEvents = filteredEvents.filter(event =>
                event.title.toLowerCase().includes(searchTerm) ||
                event.description.toLowerCase().includes(searchTerm) ||
                (event.detailedDescription && event.detailedDescription.toLowerCase().includes(searchTerm)) ||
                (event.responsiblePerson && event.responsiblePerson.toLowerCase().includes(searchTerm)) ||
                (event.affectedSystems && event.affectedSystems.toLowerCase().includes(searchTerm))
            );
        }

        if (severityFilter) {
            filteredEvents = filteredEvents.filter(event => event.severity === severityFilter);
        }

        if (filteredEvents.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="empty-state">
                        <i class="fas fa-search"></i>
                        <h3>لا توجد أحداث</h3>
                        <p>لم يتم العثور على أحداث تطابق معايير البحث</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = filteredEvents.map(event => `
            <tr>
                <td>${event.serialNumber || `SEC-${event.id}`}</td>
                <td>${event.title}</td>
                <td>${this.getTypeLabel(event.type)}</td>
                <td><span class="severity-badge severity-${event.severity}">${this.getSeverityLabel(event.severity)}</span></td>
                <td>${event.responsiblePerson || 'غير محدد'}</td>
                <td>${this.formatCostsForTable(event.directCosts, event.indirectCosts)}</td>
                <td>${this.formatDate(event.date)}</td>
                <td><span class="status-badge status-${event.status}">${this.getStatusLabel(event.status)}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view-btn" onclick="securityManager.viewEvent(${event.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${this.currentUser && this.currentUser.permissions.includes('write') ?
                            `<button class="action-btn edit-btn" onclick="securityManager.editEvent(${event.id})">
                                <i class="fas fa-edit"></i>
                            </button>` : ''
                        }
                        ${this.currentUser && this.currentUser.permissions.includes('delete') ?
                            `<button class="action-btn delete-btn" onclick="securityManager.deleteEvent(${event.id})">
                                <i class="fas fa-trash"></i>
                            </button>` : ''
                        }
                    </div>
                </td>
            </tr>
        `).join('');
    }

    filterEvents() {
        this.renderEvents();
    }

    viewEvent(eventId) {
        const event = this.events.find(e => e.id === eventId);
        if (!event) return;

        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="event-detail">
                <label>رقم التسلسل:</label>
                <div class="value">${event.serialNumber || `SEC-${event.id}`}</div>
            </div>
            <div class="event-detail">
                <label>عنوان الحدث:</label>
                <div class="value">${event.title}</div>
            </div>
            <div class="event-detail">
                <label>نوع الحدث:</label>
                <div class="value">${this.getTypeLabel(event.type)}</div>
            </div>
            <div class="event-detail">
                <label>مستوى الخطورة:</label>
                <div class="value">
                    <span class="severity-badge severity-${event.severity}">
                        ${this.getSeverityLabel(event.severity)}
                    </span>
                </div>
            </div>
            <div class="event-detail">
                <label>الشخص المسؤول عن الحدث:</label>
                <div class="value">${event.responsiblePerson || 'غير محدد'}</div>
            </div>
            <div class="event-detail">
                <label>تاريخ ووقت الحدث:</label>
                <div class="value">${this.formatDate(event.date)}</div>
            </div>
            <div class="event-detail">
                <label>وصف الحدث مختصر:</label>
                <div class="value">${event.description}</div>
            </div>
            <div class="event-detail">
                <label>وصف الحدث بالتفصيل:</label>
                <div class="value detailed-description">${event.detailedDescription || 'غير محدد'}</div>
            </div>
            <div class="event-detail">
                <label>الأنظمة المتأثرة:</label>
                <div class="value">${event.affectedSystems || 'غير محدد'}</div>
            </div>
            <div class="event-detail">
                <label>التكاليف المباشرة:</label>
                <div class="value">${event.directCosts || 'غير محدد'}</div>
            </div>
            <div class="event-detail">
                <label>التكاليف الغير مباشرة:</label>
                <div class="value">${event.indirectCosts || 'غير محدد'}</div>
            </div>
            <div class="event-detail">
                <label>الحالة:</label>
                <div class="value">
                    <span class="status-badge status-${event.status}">
                        ${this.getStatusLabel(event.status)}
                    </span>
                </div>
            </div>
            <div class="event-detail">
                <label>تاريخ الإنشاء:</label>
                <div class="value">${this.formatDate(event.createdAt)}</div>
            </div>
        `;

        this.currentViewingEventId = eventId;
        this.showModal();
    }

    editEvent(eventId) {
        // Check if user has write permission
        if (!this.currentUser || !this.currentUser.permissions.includes('write')) {
            this.showNotification('ليس لديك صلاحية لتعديل الأحداث', 'error');
            return;
        }

        const event = this.events.find(e => e.id === eventId);
        if (!event) return;

        // Fill form with event data
        document.getElementById('eventSerial').value = event.serialNumber || `SEC-${event.id}`;
        document.getElementById('eventTitle').value = event.title;
        document.getElementById('eventSeverity').value = event.severity;
        document.getElementById('eventType').value = event.type;
        document.getElementById('eventDate').value = event.date;
        document.getElementById('eventDescription').value = event.description;
        document.getElementById('eventDetailedDescription').value = event.detailedDescription || '';
        document.getElementById('affectedSystems').value = event.affectedSystems || '';
        document.getElementById('responsiblePerson').value = event.responsiblePerson || '';
        document.getElementById('directCosts').value = event.directCosts || '';
        document.getElementById('indirectCosts').value = event.indirectCosts || '';

        // Remove the event from array (will be re-added when form is submitted)
        this.events = this.events.filter(e => e.id !== eventId);
        this.saveToStorage();
        this.renderEvents();
        this.updateStatistics();
        this.updateCharts();
        this.updateRiskAnalysis();
        this.logActivity('event_edit', `تعديل حدث أمني: ${event.title}`);

        // Scroll to form
        document.querySelector('.event-input-section').scrollIntoView({ behavior: 'smooth' });
        this.showNotification('تم تحميل بيانات الحدث للتعديل', 'info');
    }

    deleteEvent(eventId) {
        // Check if user has delete permission
        if (!this.currentUser || !this.currentUser.permissions.includes('delete')) {
            this.showNotification('ليس لديك صلاحية لحذف الأحداث', 'error');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذا الحدث؟')) {
            const event = this.events.find(e => e.id === eventId);
            this.events = this.events.filter(e => e.id !== eventId);
            this.saveToStorage();
            this.renderEvents();
            this.updateStatistics();
            this.updateCharts();
            this.updateRiskAnalysis();
            if (event) {
                this.logActivity('event_delete', `حذف حدث أمني: ${event.title}`);
            }
            this.showNotification('تم حذف الحدث بنجاح', 'success');
        }
    }

    deleteCurrentEvent() {
        if (this.currentViewingEventId) {
            this.deleteEvent(this.currentViewingEventId);
            this.closeModal();
        }
    }

    showModal() {
        document.getElementById('eventModal').classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeModal() {
        document.getElementById('eventModal').classList.remove('active');
        document.body.style.overflow = 'auto';
        this.currentViewingEventId = null;
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${this.getNotificationIcon(type)}"></i>
            ${message}
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 4000);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    getSeverityLabel(severity) {
        const labels = {
            low: 'منخفض',
            medium: 'متوسط',
            high: 'عالي',
            critical: 'حرج'
        };
        return labels[severity] || severity;
    }

    getTypeLabel(type) {
        const eventType = this.eventTypes.find(et => et.key === type);
        return eventType ? eventType.label : type;
    }

    getStatusLabel(status) {
        const labels = {
            open: 'مفتوح',
            investigating: 'قيد التحقيق',
            resolved: 'محلول',
            closed: 'مغلق'
        };
        return labels[status] || status;
    }

    formatDate(dateString) {
        const date = new Date(dateString);

        // Format for Gregorian calendar in Arabic locale
        const dateOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            calendar: 'gregory'
        };

        const timeOptions = {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        };

        const formattedDate = date.toLocaleDateString('ar-EG', dateOptions);
        const formattedTime = date.toLocaleTimeString('ar-EG', timeOptions);

        return `${formattedDate} - ${formattedTime}`;
    }

    formatCostsForTable(directCosts, indirectCosts) {
        const direct = directCosts && directCosts.trim() ? directCosts.trim() : '';
        const indirect = indirectCosts && indirectCosts.trim() ? indirectCosts.trim() : '';

        if (!direct && !indirect) {
            return '<span class="no-costs">غير محدد</span>';
        }

        let result = '';
        if (direct) {
            result += `<div class="cost-item direct"><strong>مباشرة:</strong> ${direct}</div>`;
        }
        if (indirect) {
            result += `<div class="cost-item indirect"><strong>غير مباشرة:</strong> ${indirect}</div>`;
        }

        return result;
    }

    showAlert(message, type = 'info') {
        // Create alert element if it doesn't exist
        let alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) {
            alertContainer = document.createElement('div');
            alertContainer.id = 'alertContainer';
            alertContainer.className = 'alert-container';
            document.body.appendChild(alertContainer);
        }

        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button class="alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        alertContainer.appendChild(alert);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    }

    saveToStorage() {
        localStorage.setItem('securityEvents', JSON.stringify(this.events));
        localStorage.setItem('currentEventId', this.currentEventId.toString());
    }

    // Navigation between sections
    switchSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        // Remove active class from all nav buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Show selected section
        document.getElementById(`${sectionName}-section`).classList.add('active');

        // Add active class to clicked nav button
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update data when switching to analytics or risk analysis
        if (sectionName === 'analytics') {
            this.updateStatistics();
            this.updateCharts();
        } else if (sectionName === 'risk-analysis') {
            this.updateRiskAnalysis();
        } else if (sectionName === 'post-incident-review') {
            // Re-initialize post incident review when switching to this section
            setTimeout(() => {
                this.initPostIncidentReview();
            }, 200);
        } else if (sectionName === 'user-management') {
            this.renderUsers();
        } else if (sectionName === 'event-types') {
            this.renderEventTypes();
        }
    }

    // Logout function
    logout() {
        if (this.currentUser) {
            this.logActivity('logout', `تسجيل خروج المستخدم ${this.currentUser.fullName}`);
        }

        const sessionId = localStorage.getItem('currentSession');
        if (sessionId) {
            this.clearSession(sessionId);
        }

        this.currentUser = null;
        window.location.href = 'login.html';
    }

    // Log activity
    logActivity(action, description) {
        const activityLog = JSON.parse(localStorage.getItem('activityLog')) || [];
        const activity = {
            id: Date.now(),
            userId: this.currentUser ? this.currentUser.id : 'anonymous',
            username: this.currentUser ? this.currentUser.username : 'anonymous',
            action: action,
            description: description,
            timestamp: new Date().toISOString(),
            ip: 'localhost',
            userAgent: navigator.userAgent
        };

        activityLog.unshift(activity);

        if (activityLog.length > 1000) {
            activityLog.splice(1000);
        }

        localStorage.setItem('activityLog', JSON.stringify(activityLog));
    }

    // User Management Functions
    showUserModal(user = null) {
        const modal = document.getElementById('userModal');
        const title = document.getElementById('userModalTitle');
        const form = document.getElementById('userForm');

        if (user) {
            title.textContent = 'تعديل المستخدم';
            this.fillUserForm(user);
        } else {
            title.textContent = 'إضافة مستخدم جديد';
            form.reset();
        }

        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeUserModal() {
        const modal = document.getElementById('userModal');
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
        document.getElementById('userForm').reset();
    }

    fillUserForm(user) {
        document.getElementById('userFullName').value = user.fullName;
        document.getElementById('userUsername').value = user.username;
        document.getElementById('userEmail').value = user.email;
        document.getElementById('userRole').value = user.role;

        // Set permissions
        document.querySelectorAll('input[name="permissions"]').forEach(checkbox => {
            checkbox.checked = user.permissions.includes(checkbox.value);
        });
    }

    updatePermissionsByRole() {
        const role = document.getElementById('userRole').value;
        const permissionCheckboxes = document.querySelectorAll('input[name="permissions"]');

        // Clear all permissions first
        permissionCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // Set permissions based on role
        const rolePermissions = {
            'admin': ['read', 'write', 'delete', 'manage_users', 'view_analytics', 'manage_system'],
            'analyst': ['read', 'write', 'view_analytics'],
            'operator': ['read']
        };

        if (rolePermissions[role]) {
            rolePermissions[role].forEach(permission => {
                const checkbox = document.querySelector(`input[name="permissions"][value="${permission}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
        }
    }

    saveUser() {
        const form = document.getElementById('userForm');
        const formData = new FormData(form);

        const username = formData.get('username').trim();
        const fullName = formData.get('fullName').trim();
        const email = formData.get('email').trim();
        const role = formData.get('role');
        const password = formData.get('password');

        if (!username || !fullName || !email || !role || !password) {
            this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        const permissions = Array.from(document.querySelectorAll('input[name="permissions"]:checked'))
            .map(checkbox => checkbox.value);

        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};

        // Check if username already exists (for new users)
        const isEditing = users[username] && users[username].id;
        if (!isEditing && users[username]) {
            this.showNotification('اسم المستخدم موجود بالفعل', 'error');
            return;
        }

        const user = {
            id: isEditing ? users[username].id : username,
            username: username,
            password: this.hashPassword(password),
            fullName: fullName,
            email: email,
            role: role,
            permissions: permissions,
            isActive: true,
            createdAt: isEditing ? users[username].createdAt : new Date().toISOString(),
            lastLogin: isEditing ? users[username].lastLogin : null
        };

        users[username] = user;
        localStorage.setItem('systemUsers', JSON.stringify(users));

        this.logActivity('user_management',
            isEditing ? `تعديل المستخدم ${fullName}` : `إضافة المستخدم ${fullName}`);

        this.closeUserModal();
        this.renderUsers();
        this.showNotification(
            isEditing ? 'تم تحديث المستخدم بنجاح' : 'تم إضافة المستخدم بنجاح',
            'success'
        );
    }

    hashPassword(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString();
    }

    renderUsers() {
        const usersGrid = document.getElementById('usersGrid');
        if (!usersGrid) return;

        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
        const usersList = Object.values(users);

        if (usersList.length === 0) {
            usersGrid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h3>لا يوجد مستخدمين</h3>
                    <p>اضغط على "إضافة مستخدم جديد" لإضافة أول مستخدم</p>
                </div>
            `;
            return;
        }

        usersGrid.innerHTML = usersList.map(user => `
            <div class="user-card ${!user.isActive ? 'inactive' : ''}">
                <div class="user-header">
                    <div class="user-avatar">
                        ${user.fullName.charAt(0).toUpperCase()}
                    </div>
                    <div class="user-info">
                        <h4>${user.fullName}</h4>
                        <p>@${user.username}</p>
                    </div>
                </div>
                <div class="user-role role-${user.role}">
                    ${this.getRoleLabel(user.role)}
                </div>
                <div class="user-permissions">
                    <h5>الصلاحيات:</h5>
                    <div class="permission-tags">
                        ${user.permissions.map(permission =>
                            `<span class="permission-tag">${this.getPermissionLabel(permission)}</span>`
                        ).join('')}
                    </div>
                </div>
                <div class="user-actions">
                    <button class="user-action-btn edit-user-btn" onclick="securityManager.editUser('${user.username}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="user-action-btn toggle-user-btn" onclick="securityManager.toggleUser('${user.username}')">
                        <i class="fas fa-${user.isActive ? 'user-slash' : 'user-check'}"></i>
                    </button>
                    ${user.username !== this.currentUser.username ?
                        `<button class="user-action-btn delete-user-btn" onclick="securityManager.deleteUser('${user.username}')">
                            <i class="fas fa-trash"></i>
                        </button>` : ''
                    }
                </div>
            </div>
        `).join('');
    }

    editUser(username) {
        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
        const user = users[username];
        if (user) {
            this.showUserModal(user);
        }
    }

    toggleUser(username) {
        if (username === this.currentUser.username) {
            this.showNotification('لا يمكن تعطيل حسابك الخاص', 'error');
            return;
        }

        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
        const user = users[username];

        if (user) {
            user.isActive = !user.isActive;
            users[username] = user;
            localStorage.setItem('systemUsers', JSON.stringify(users));

            this.logActivity('user_management',
                `${user.isActive ? 'تفعيل' : 'تعطيل'} المستخدم ${user.fullName}`);

            this.renderUsers();
            this.showNotification(
                `تم ${user.isActive ? 'تفعيل' : 'تعطيل'} المستخدم بنجاح`,
                'success'
            );
        }
    }

    deleteUser(username) {
        if (username === this.currentUser.username) {
            this.showNotification('لا يمكن حذف حسابك الخاص', 'error');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
            const user = users[username];

            if (user) {
                delete users[username];
                localStorage.setItem('systemUsers', JSON.stringify(users));

                this.logActivity('user_management', `حذف المستخدم ${user.fullName}`);

                this.renderUsers();
                this.showNotification('تم حذف المستخدم بنجاح', 'success');
            }
        }
    }

    getRoleLabel(role) {
        const labels = {
            'admin': 'مدير النظام',
            'analyst': 'محلل أمني',
            'operator': 'مشغل'
        };
        return labels[role] || role;
    }

    getPermissionLabel(permission) {
        const labels = {
            'read': 'قراءة',
            'write': 'كتابة',
            'delete': 'حذف',
            'view_analytics': 'عرض الإحصائيات',
            'manage_users': 'إدارة المستخدمين',
            'manage_system': 'إدارة النظام'
        };
        return labels[permission] || permission;
    }

    // Event Types Management Functions
    showEventTypeModal(eventTypeKey = null) {
        const modal = document.getElementById('eventTypeModal');
        const title = document.getElementById('eventTypeModalTitle');
        const form = document.getElementById('eventTypeForm');

        if (eventTypeKey) {
            const eventType = this.eventTypes.find(et => et.key === eventTypeKey);
            if (eventType) {
                title.textContent = 'تعديل نوع الحدث';
                this.fillEventTypeForm(eventType);
                this.currentEditingEventTypeKey = eventTypeKey;
                // Disable key field when editing
                document.getElementById('eventTypeKey').disabled = true;
            }
        } else {
            title.textContent = 'إضافة نوع حدث جديد';
            form.reset();
            this.currentEditingEventTypeKey = null;
            document.getElementById('eventTypeKey').disabled = false;
        }

        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeEventTypeModal() {
        const modal = document.getElementById('eventTypeModal');
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
        document.getElementById('eventTypeForm').reset();
        this.currentEditingEventTypeKey = null;
    }

    fillEventTypeForm(eventType) {
        document.getElementById('eventTypeKey').value = eventType.key;
        document.getElementById('eventTypeLabel').value = eventType.label;
        document.getElementById('eventTypeDescription').value = eventType.description || '';
        document.getElementById('eventTypeIcon').value = eventType.icon;
        document.getElementById('eventTypeColor').value = eventType.color;
    }

    saveEventType(event) {
        event.preventDefault();

        const formData = new FormData(document.getElementById('eventTypeForm'));
        const key = formData.get('eventTypeKey').trim();
        const label = formData.get('eventTypeLabel').trim();
        const description = formData.get('eventTypeDescription').trim();
        const icon = formData.get('eventTypeIcon');
        const color = formData.get('eventTypeColor');

        if (!key || !label || !icon || !color) {
            this.showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        // Check if key already exists (for new types)
        const isEditing = this.currentEditingEventTypeKey !== null;
        if (!isEditing && this.eventTypes.some(et => et.key === key)) {
            this.showAlert('مفتاح النوع موجود بالفعل، يرجى استخدام مفتاح مختلف', 'error');
            return;
        }

        const eventType = {
            key: key,
            label: label,
            description: description,
            icon: icon,
            color: color
        };

        if (isEditing) {
            const index = this.eventTypes.findIndex(et => et.key === this.currentEditingEventTypeKey);
            if (index !== -1) {
                this.eventTypes[index] = eventType;
            }
        } else {
            this.eventTypes.push(eventType);
        }

        this.saveEventTypesToStorage();
        this.updateEventTypeDropdown();
        this.renderEventTypes();
        this.closeEventTypeModal();

        this.showAlert(
            isEditing ? 'تم تحديث نوع الحدث بنجاح' : 'تم إضافة نوع الحدث بنجاح',
            'success'
        );

        this.logActivity('event_type_management',
            isEditing ? `تعديل نوع الحدث ${label}` : `إضافة نوع الحدث ${label}`);
    }

    renderEventTypes() {
        const grid = document.getElementById('eventTypesGrid');
        if (!grid) return;

        if (this.eventTypes.length === 0) {
            grid.innerHTML = `
                <div class="empty-event-types">
                    <i class="fas fa-tags"></i>
                    <h3>لا توجد أنواع أحداث</h3>
                    <p>اضغط على "إضافة نوع جديد" لإضافة أول نوع حدث</p>
                </div>
            `;
            return;
        }

        grid.innerHTML = this.eventTypes.map(eventType => {
            const usageCount = this.events.filter(e => e.type === eventType.key).length;
            return `
                <div class="event-type-card">
                    <div class="event-type-usage">${usageCount} حدث</div>
                    <div class="event-type-header">
                        <div class="event-type-icon" style="background-color: ${eventType.color}">
                            <i class="${eventType.icon}"></i>
                        </div>
                        <div class="event-type-info">
                            <h3>${eventType.label}</h3>
                            <div class="event-type-key">${eventType.key}</div>
                        </div>
                    </div>
                    <div class="event-type-description">
                        ${eventType.description || 'لا يوجد وصف'}
                    </div>
                    <div class="event-type-actions">
                        <button class="action-btn edit-type-btn" onclick="securityManager.editEventType('${eventType.key}')">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </button>
                        ${usageCount === 0 ?
                            `<button class="action-btn delete-type-btn" onclick="securityManager.deleteEventType('${eventType.key}')">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>` :
                            `<button class="action-btn delete-type-btn" disabled title="لا يمكن حذف نوع مستخدم في الأحداث">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>`
                        }
                    </div>
                </div>
            `;
        }).join('');
    }

    editEventType(eventTypeKey) {
        this.showEventTypeModal(eventTypeKey);
    }

    deleteEventType(eventTypeKey) {
        const eventType = this.eventTypes.find(et => et.key === eventTypeKey);
        if (!eventType) return;

        // Check if type is used in any events
        const usageCount = this.events.filter(e => e.type === eventTypeKey).length;
        if (usageCount > 0) {
            this.showAlert('لا يمكن حذف نوع الحدث لأنه مستخدم في الأحداث الموجودة', 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف نوع الحدث "${eventType.label}"؟`)) {
            this.eventTypes = this.eventTypes.filter(et => et.key !== eventTypeKey);
            this.saveEventTypesToStorage();
            this.updateEventTypeDropdown();
            this.renderEventTypes();
            this.showAlert('تم حذف نوع الحدث بنجاح', 'success');
            this.logActivity('event_type_management', `حذف نوع الحدث ${eventType.label}`);
        }
    }

    updateEventTypeDropdown() {
        const dropdown = document.getElementById('eventType');
        if (!dropdown) return;

        // Save current selection
        const currentValue = dropdown.value;

        // Clear and rebuild options
        dropdown.innerHTML = '<option value="">اختر نوع الحدث</option>';

        this.eventTypes.forEach(eventType => {
            const option = document.createElement('option');
            option.value = eventType.key;
            option.textContent = eventType.label;
            dropdown.appendChild(option);
        });

        // Restore selection if still valid
        if (currentValue && this.eventTypes.some(et => et.key === currentValue)) {
            dropdown.value = currentValue;
        }
    }

    saveEventTypesToStorage() {
        localStorage.setItem('eventTypes', JSON.stringify(this.eventTypes));
    }

    // Update statistics
    updateStatistics() {
        try {
            const totalEvents = this.events.length;
            const criticalEvents = this.events.filter(e => e.severity === 'critical').length;
            const highEvents = this.events.filter(e => e.severity === 'high').length;
            const openEvents = this.events.filter(e => e.status === 'open').length;

            const totalEl = document.getElementById('totalEvents');
            const criticalEl = document.getElementById('criticalEvents');
            const highEl = document.getElementById('highEvents');
            const openEl = document.getElementById('openEvents');

            if (totalEl) totalEl.textContent = totalEvents;
            if (criticalEl) criticalEl.textContent = criticalEvents;
            if (highEl) highEl.textContent = highEvents;
            if (openEl) openEl.textContent = openEvents;
        } catch (error) {
            console.error('Error updating statistics:', error);
        }
    }

    // Initialize charts
    initializeCharts() {
        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not loaded');
            return;
        }

        this.charts = {};

        // Add delay to ensure DOM is ready
        setTimeout(() => {
            this.createSeverityChart();
            this.createTimelineChart();
            this.createTypeChart();
            this.createStatusChart();
            this.createRiskGauge();
        }, 100);
    }

    // Update all charts
    updateCharts() {
        try {
            if (this.charts && this.charts.severityChart) {
                this.updateSeverityChart();
            }
            if (this.charts && this.charts.timelineChart) {
                this.updateTimelineChart();
            }
            if (this.charts && this.charts.typeChart) {
                this.updateTypeChart();
            }
            if (this.charts && this.charts.statusChart) {
                this.updateStatusChart();
            }
        } catch (error) {
            console.error('Error updating charts:', error);
        }
    }

    // Create severity distribution chart
    createSeverityChart() {
        try {
            const canvas = document.getElementById('severityChart');
            if (!canvas) {
                console.error('Severity chart canvas not found');
                return;
            }
            const ctx = canvas.getContext('2d');
            this.charts.severityChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['منخفض', 'متوسط', 'عالي', 'حرج'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        '#10b981',
                        '#f59e0b',
                        '#ef4444',
                        '#dc2626'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
        this.updateSeverityChart();
        } catch (error) {
            console.error('Error creating severity chart:', error);
        }
    }

    // Update severity chart data
    updateSeverityChart() {
        if (!this.charts.severityChart) return;

        try {
            const severityCounts = {
                low: this.events.filter(e => e.severity === 'low').length,
                medium: this.events.filter(e => e.severity === 'medium').length,
                high: this.events.filter(e => e.severity === 'high').length,
                critical: this.events.filter(e => e.severity === 'critical').length
            };

            this.charts.severityChart.data.datasets[0].data = [
                severityCounts.low,
                severityCounts.medium,
                severityCounts.high,
                severityCounts.critical
            ];
            this.charts.severityChart.update();
        } catch (error) {
            console.error('Error updating severity chart:', error);
        }
    }

    // Create timeline chart
    createTimelineChart() {
        try {
            const canvas = document.getElementById('timelineChart');
            if (!canvas) {
                console.error('Timeline chart canvas not found');
                return;
            }
            const ctx = canvas.getContext('2d');
        this.charts.timelineChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'عدد الأحداث',
                    data: [],
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        this.updateTimelineChart();
        } catch (error) {
            console.error('Error creating timeline chart:', error);
        }
    }

    // Update timeline chart data
    updateTimelineChart() {
        if (!this.charts.timelineChart) return;

        try {
            const last7Days = [];
            const eventCounts = [];

            for (let i = 6; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];
                last7Days.push(date.toLocaleDateString('ar-EG', {
                    month: 'short',
                    day: 'numeric',
                    calendar: 'gregory'
                }));

                const dayEvents = this.events.filter(event => {
                    const eventDate = new Date(event.date).toISOString().split('T')[0];
                    return eventDate === dateStr;
                }).length;

                eventCounts.push(dayEvents);
            }

            this.charts.timelineChart.data.labels = last7Days;
            this.charts.timelineChart.data.datasets[0].data = eventCounts;
            this.charts.timelineChart.update();
        } catch (error) {
            console.error('Error updating timeline chart:', error);
        }
    }

    // Create event type chart
    createTypeChart() {
        try {
            const canvas = document.getElementById('typeChart');
            if (!canvas) {
                console.error('Type chart canvas not found');
                return;
            }
            const ctx = canvas.getContext('2d');
        this.charts.typeChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['اختراق', 'برمجيات خبيثة', 'تصيد', 'تسريب بيانات', 'وصول غير مصرح', 'فشل النظام', 'أخرى'],
                datasets: [{
                    label: 'عدد الأحداث',
                    data: [0, 0, 0, 0, 0, 0, 0],
                    backgroundColor: [
                        '#ef4444',
                        '#f59e0b',
                        '#06b6d4',
                        '#dc2626',
                        '#8b5cf6',
                        '#6b7280',
                        '#10b981'
                    ],
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        this.updateTypeChart();
        } catch (error) {
            console.error('Error creating type chart:', error);
        }
    }

    // Update type chart data
    updateTypeChart() {
        if (!this.charts.typeChart) return;

        try {
            const typeCounts = {
                intrusion: this.events.filter(e => e.type === 'intrusion').length,
                malware: this.events.filter(e => e.type === 'malware').length,
                phishing: this.events.filter(e => e.type === 'phishing').length,
                'data-breach': this.events.filter(e => e.type === 'data-breach').length,
                'unauthorized-access': this.events.filter(e => e.type === 'unauthorized-access').length,
                'system-failure': this.events.filter(e => e.type === 'system-failure').length,
                other: this.events.filter(e => e.type === 'other').length
            };

            this.charts.typeChart.data.datasets[0].data = [
                typeCounts.intrusion,
                typeCounts.malware,
                typeCounts.phishing,
                typeCounts['data-breach'],
                typeCounts['unauthorized-access'],
                typeCounts['system-failure'],
                typeCounts.other
            ];
            this.charts.typeChart.update();
        } catch (error) {
            console.error('Error updating type chart:', error);
        }
    }

    // Create status chart
    createStatusChart() {
        try {
            const canvas = document.getElementById('statusChart');
            if (!canvas) {
                console.error('Status chart canvas not found');
                return;
            }
            const ctx = canvas.getContext('2d');
        this.charts.statusChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['مفتوح', 'قيد التحقيق', 'محلول', 'مغلق'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        '#2563eb',
                        '#f59e0b',
                        '#10b981',
                        '#6b7280'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
        this.updateStatusChart();
        } catch (error) {
            console.error('Error creating status chart:', error);
        }
    }

    // Update status chart data
    updateStatusChart() {
        if (!this.charts.statusChart) return;

        try {
            const statusCounts = {
                open: this.events.filter(e => e.status === 'open').length,
                investigating: this.events.filter(e => e.status === 'investigating').length,
                resolved: this.events.filter(e => e.status === 'resolved').length,
                closed: this.events.filter(e => e.status === 'closed').length
            };

            this.charts.statusChart.data.datasets[0].data = [
                statusCounts.open,
                statusCounts.investigating,
                statusCounts.resolved,
                statusCounts.closed
            ];
            this.charts.statusChart.update();
        } catch (error) {
            console.error('Error updating status chart:', error);
        }
    }

    // Create risk gauge
    createRiskGauge() {
        try {
            const canvas = document.getElementById('riskGauge');
            if (!canvas) {
                console.error('Risk gauge canvas not found');
                return;
            }
            const ctx = canvas.getContext('2d');
        this.charts.riskGauge = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [65, 35],
                    backgroundColor: [
                        '#f59e0b',
                        '#e5e7eb'
                    ],
                    borderWidth: 0,
                    circumference: 180,
                    rotation: 270
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '75%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                }
            }
        });
        } catch (error) {
            console.error('Error creating risk gauge:', error);
        }
    }

    // Update risk analysis
    updateRiskAnalysis() {
        const totalEvents = this.events.length;
        const criticalEvents = this.events.filter(e => e.severity === 'critical').length;
        const highEvents = this.events.filter(e => e.severity === 'high').length;
        const openEvents = this.events.filter(e => e.status === 'open').length;

        // Calculate risk score (0-100)
        let riskScore = 0;
        if (totalEvents > 0) {
            riskScore = Math.min(100,
                (criticalEvents * 25) +
                (highEvents * 15) +
                (openEvents * 10) +
                (totalEvents * 2)
            );
        }

        // Update risk level and description
        let riskLevel, riskDescription, riskColor;
        if (riskScore >= 80) {
            riskLevel = 'حرج';
            riskDescription = 'مستوى مخاطر عالي جداً - يتطلب تدخل فوري';
            riskColor = '#dc2626';
        } else if (riskScore >= 60) {
            riskLevel = 'عالي';
            riskDescription = 'مستوى مخاطر عالي - يتطلب اهتماماً عاجلاً';
            riskColor = '#ef4444';
        } else if (riskScore >= 40) {
            riskLevel = 'متوسط';
            riskDescription = 'مستوى مخاطر متوسط - يتطلب مراقبة';
            riskColor = '#f59e0b';
        } else {
            riskLevel = 'منخفض';
            riskDescription = 'مستوى مخاطر منخفض - الوضع مستقر';
            riskColor = '#10b981';
        }

        const riskLevelEl = document.getElementById('riskLevel');
        const riskScoreEl = document.getElementById('riskScore');
        const riskDescEl = document.getElementById('riskDescription');

        if (riskLevelEl) {
            riskLevelEl.textContent = riskLevel;
            riskLevelEl.style.color = riskColor;
        }
        if (riskScoreEl) riskScoreEl.textContent = `${riskScore}/100`;
        if (riskDescEl) riskDescEl.textContent = riskDescription;

        // Update risk gauge
        try {
            if (this.charts && this.charts.riskGauge) {
                this.charts.riskGauge.data.datasets[0].data = [riskScore, 100 - riskScore];
                this.charts.riskGauge.data.datasets[0].backgroundColor[0] = riskColor;
                this.charts.riskGauge.update();
            }
        } catch (error) {
            console.error('Error updating risk gauge:', error);
        }

        // Generate recommendations
        this.generateRecommendations(riskScore, criticalEvents, highEvents, openEvents);
    }

    // Generate security recommendations
    generateRecommendations(riskScore, criticalEvents, highEvents, openEvents) {
        const recommendations = [];

        if (criticalEvents > 0) {
            recommendations.push({
                title: 'معالجة الأحداث الحرجة فوراً',
                description: `يوجد ${criticalEvents} حدث حرج يتطلب معالجة فورية لتجنب تفاقم المخاطر الأمنية.`,
                priority: 'high'
            });
        }

        if (openEvents > 5) {
            recommendations.push({
                title: 'مراجعة الأحداث المفتوحة',
                description: `يوجد ${openEvents} حدث مفتوح. يُنصح بمراجعة وإغلاق الأحداث المحلولة.`,
                priority: 'medium'
            });
        }

        if (riskScore > 70) {
            recommendations.push({
                title: 'تعزيز الإجراءات الأمنية',
                description: 'مستوى المخاطر مرتفع. يُنصح بتطبيق إجراءات أمنية إضافية ومراجعة السياسات.',
                priority: 'high'
            });
        }

        if (this.events.filter(e => e.type === 'unauthorized-access').length > 2) {
            recommendations.push({
                title: 'تقوية أنظمة المصادقة',
                description: 'تم رصد محاولات وصول غير مصرح متعددة. يُنصح بتطبيق المصادقة الثنائية.',
                priority: 'medium'
            });
        }

        if (recommendations.length === 0) {
            recommendations.push({
                title: 'الحفاظ على المراقبة المستمرة',
                description: 'الوضع الأمني مستقر حالياً. استمر في المراقبة والتحديث المنتظم للأنظمة.',
                priority: 'low'
            });
        }

        this.renderRecommendations(recommendations);
    }

    // Render recommendations
    renderRecommendations(recommendations) {
        const container = document.getElementById('recommendationsList');
        if (!container) return;

        try {
            container.innerHTML = recommendations.map(rec => `
                <div class="recommendation-item ${rec.priority}-priority">
                    <div class="recommendation-header">
                        <div class="recommendation-title">${rec.title}</div>
                        <span class="recommendation-priority ${rec.priority}">${this.getPriorityLabel(rec.priority)}</span>
                    </div>
                    <div class="recommendation-description">${rec.description}</div>
                </div>
            `).join('');
        } catch (error) {
            console.error('Error rendering recommendations:', error);
        }
    }

    // Get priority label
    getPriorityLabel(priority) {
        const labels = {
            high: 'عالي',
            medium: 'متوسط',
            low: 'منخفض'
        };
        return labels[priority] || priority;
    }

    saveToStorage() {
        try {
            localStorage.setItem('securityEvents', JSON.stringify(this.events));
            localStorage.setItem('currentEventId', this.currentEventId.toString());
        } catch (error) {
            console.error('Error saving to localStorage:', error);
            this.showNotification('خطأ في حفظ البيانات', 'error');
        }
    }

    // Initialize Post Incident Review functionality
    initPostIncidentReview() {
        try {
            // Initialize reviews array if not exists
            if (!this.postIncidentReviews) {
                this.postIncidentReviews = JSON.parse(localStorage.getItem('postIncidentReviews')) || [];
            }

            // Setup form submission with delay to ensure DOM is ready
            setTimeout(() => {
                const form = document.getElementById('postIncidentForm');
                if (form) {
                    // Remove any existing listeners
                    form.removeEventListener('submit', this.handlePostIncidentSubmit);
                    // Add new listener
                    form.addEventListener('submit', (e) => this.handlePostIncidentSubmit(e));
                    console.log('Post incident form listener attached successfully');
                } else {
                    console.warn('Post incident form not found');
                }

                // Setup direct button click handler as backup
                const saveBtn = document.getElementById('saveReviewBtn');
                if (saveBtn) {
                    saveBtn.addEventListener('click', (e) => {
                        console.log('Save button clicked directly');
                        // Prevent default button behavior
                        e.preventDefault();

                        // Get the form and trigger submit event
                        const form = document.getElementById('postIncidentForm');
                        if (form) {
                            // Create a synthetic submit event
                            const submitEvent = new Event('submit', {
                                bubbles: true,
                                cancelable: true
                            });
                            form.dispatchEvent(submitEvent);
                        }
                    });
                }

                // Setup search and filter
                const searchInput = document.getElementById('searchReviews');
                const filterSelect = document.getElementById('filterReviewType');

                if (searchInput) {
                    searchInput.addEventListener('input', () => this.filterReviews());
                }

                if (filterSelect) {
                    filterSelect.addEventListener('change', () => this.filterReviews());
                }

                // Render existing reviews
                this.renderReviews();
            }, 100);

        } catch (error) {
            console.error('Error initializing post incident review:', error);
        }
    }

    // Handle post incident review form submission
    handlePostIncidentSubmit(e) {
        e.preventDefault();
        console.log('Form submission started');

        // Add loading state to save button
        const saveBtn = document.getElementById('saveReviewBtn');
        if (saveBtn) {
            saveBtn.classList.add('btn-loading');
            saveBtn.disabled = true;
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        }

        try {
            const formData = new FormData(e.target);

            // Validate required fields
            const requiredFields = [
                'reviewerName', 'reviewerDepartment', 'reviewerManagement',
                'incidentDate', 'incidentTime', 'incidentLocation',
                'incidentDescription', 'incidentNumber', 'incidentType',
                'incidentResponsible', 'employeePreparedness', 'responseQuality',
                'apparentCauses', 'rootCauses', 'contributingFactors',
                'recoveryPlans', 'recoveryEffectiveness', 'correctiveActions',
                'actionResponsible', 'implementationPeriod', 'followupMechanism',
                'followupResponsible', 'followupTiming', 'lessonsLearned'
            ];

            const missingFields = [];
            for (const field of requiredFields) {
                const value = formData.get(field);
                if (!value || value.trim() === '') {
                    missingFields.push(field);
                }
            }

            if (missingFields.length > 0) {
                this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                console.log('Missing fields:', missingFields);
                return;
            }

            const reviewData = {
                id: Date.now(),
                reviewerName: formData.get('reviewerName').trim(),
                reviewerDepartment: formData.get('reviewerDepartment').trim(),
                reviewerManagement: formData.get('reviewerManagement').trim(),
                incidentDate: formData.get('incidentDate'),
                incidentTime: formData.get('incidentTime'),
                incidentLocation: formData.get('incidentLocation').trim(),
                incidentDescription: formData.get('incidentDescription').trim(),
                incidentNumber: formData.get('incidentNumber').trim(),
                incidentType: formData.get('incidentType'),
                incidentResponsible: formData.get('incidentResponsible').trim(),
                employeePreparedness: formData.get('employeePreparedness'),
                responseQuality: formData.get('responseQuality'),
                apparentCauses: formData.get('apparentCauses').trim(),
                rootCauses: formData.get('rootCauses').trim(),
                contributingFactors: formData.get('contributingFactors').trim(),
                recoveryPlans: formData.get('recoveryPlans').trim(),
                recoveryEffectiveness: formData.get('recoveryEffectiveness').trim(),
                correctiveActions: formData.get('correctiveActions').trim(),
                actionResponsible: formData.get('actionResponsible').trim(),
                implementationPeriod: formData.get('implementationPeriod').trim(),
                followupMechanism: formData.get('followupMechanism').trim(),
                followupResponsible: formData.get('followupResponsible').trim(),
                followupTiming: formData.get('followupTiming').trim(),
                lessonsLearned: formData.get('lessonsLearned').trim(),
                createdAt: new Date().toISOString(),
                createdBy: this.currentUser?.username || 'مجهول'
            };

            console.log('Review data prepared:', reviewData);

            // Initialize array if needed
            if (!this.postIncidentReviews) {
                this.postIncidentReviews = [];
            }

            // Add to reviews array
            this.postIncidentReviews.push(reviewData);
            console.log('Review added to array. Total reviews:', this.postIncidentReviews.length);

            // Save to localStorage
            localStorage.setItem('postIncidentReviews', JSON.stringify(this.postIncidentReviews));
            console.log('Review saved to localStorage');

            // Reset form
            e.target.reset();
            console.log('Form reset');

            // Render updated reviews
            this.renderReviews();
            console.log('Reviews rendered');

            // Show success notification
            this.showNotification('تم حفظ المراجعة بنجاح', 'success');
            console.log('Success notification shown');

        } catch (error) {
            console.error('Error saving post incident review:', error);
            this.showNotification('خطأ في حفظ المراجعة: ' + error.message, 'error');
        } finally {
            // Remove loading state from save button
            const saveBtn = document.getElementById('saveReviewBtn');
            if (saveBtn) {
                setTimeout(() => {
                    saveBtn.classList.remove('btn-loading');
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ المراجعة';
                }, 500); // Small delay to show the loading effect
            }
        }
    }

    // Render post incident reviews
    renderReviews() {
        try {
            const tbody = document.getElementById('reviewsTableBody');
            if (!tbody) return;

            if (!this.postIncidentReviews || this.postIncidentReviews.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                            <i class="fas fa-clipboard-list" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                            لا توجد مراجعات محفوظة
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = this.postIncidentReviews.map(review => `
                <tr>
                    <td>${review.incidentNumber}</td>
                    <td>${this.getEventTypeLabel(review.incidentType)}</td>
                    <td>${review.reviewerName}</td>
                    <td>${this.formatDate(review.incidentDate + 'T' + review.incidentTime)}</td>
                    <td><span class="preparedness-badge ${review.employeePreparedness}">${this.getPreparednessLabel(review.employeePreparedness)}</span></td>
                    <td><span class="response-badge ${review.responseQuality}">${this.getResponseLabel(review.responseQuality)}</span></td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="securityManager.viewReviewDetails(${review.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="securityManager.deleteReview(${review.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');

        } catch (error) {
            console.error('Error rendering reviews:', error);
        }
    }

    // Get preparedness label
    getPreparednessLabel(value) {
        const labels = {
            'prepared': 'مستعد',
            'partial': 'جزئي',
            'unprepared': 'غير مستعد'
        };
        return labels[value] || value;
    }

    // Get response quality label
    getResponseLabel(value) {
        const labels = {
            'complete': 'كامل',
            'partial': 'جزئي',
            'poor': 'سيئ الاستجابة'
        };
        return labels[value] || value;
    }

    // Filter reviews based on search and type
    filterReviews() {
        try {
            const searchTerm = document.getElementById('searchReviews')?.value.toLowerCase() || '';
            const filterType = document.getElementById('filterReviewType')?.value || '';

            let filteredReviews = this.postIncidentReviews || [];

            // Apply search filter
            if (searchTerm) {
                filteredReviews = filteredReviews.filter(review =>
                    review.incidentNumber.toLowerCase().includes(searchTerm) ||
                    review.reviewerName.toLowerCase().includes(searchTerm) ||
                    review.incidentDescription.toLowerCase().includes(searchTerm) ||
                    this.getEventTypeLabel(review.incidentType).toLowerCase().includes(searchTerm)
                );
            }

            // Apply type filter
            if (filterType) {
                filteredReviews = filteredReviews.filter(review => review.incidentType === filterType);
            }

            // Render filtered results
            this.renderFilteredReviews(filteredReviews);

        } catch (error) {
            console.error('Error filtering reviews:', error);
        }
    }

    // Render filtered reviews
    renderFilteredReviews(reviews) {
        try {
            const tbody = document.getElementById('reviewsTableBody');
            if (!tbody) return;

            if (reviews.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                            <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                            لا توجد نتائج مطابقة للبحث
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = reviews.map(review => `
                <tr>
                    <td>${review.incidentNumber}</td>
                    <td>${this.getEventTypeLabel(review.incidentType)}</td>
                    <td>${review.reviewerName}</td>
                    <td>${this.formatDate(review.incidentDate + 'T' + review.incidentTime)}</td>
                    <td><span class="preparedness-badge ${review.employeePreparedness}">${this.getPreparednessLabel(review.employeePreparedness)}</span></td>
                    <td><span class="response-badge ${review.responseQuality}">${this.getResponseLabel(review.responseQuality)}</span></td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="securityManager.viewReviewDetails(${review.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="securityManager.deleteReview(${review.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');

        } catch (error) {
            console.error('Error rendering filtered reviews:', error);
        }
    }

    // View review details
    viewReviewDetails(reviewId) {
        try {
            const review = this.postIncidentReviews.find(r => r.id === reviewId);
            if (!review) {
                this.showNotification('لم يتم العثور على المراجعة', 'error');
                return;
            }

            // Create modal for review details
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content review-details-modal">
                    <div class="modal-header">
                        <h3><i class="fas fa-clipboard-check"></i> تفاصيل المراجعة - ${review.incidentNumber}</h3>
                        <button class="close-btn" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="review-details-grid">
                            <div class="detail-section">
                                <h4><i class="fas fa-user"></i> المعلومات الشخصية</h4>
                                <div class="detail-item"><strong>الاسم:</strong> ${review.reviewerName}</div>
                                <div class="detail-item"><strong>القسم:</strong> ${review.reviewerDepartment}</div>
                                <div class="detail-item"><strong>الإدارة:</strong> ${review.reviewerManagement}</div>
                            </div>

                            <div class="detail-section">
                                <h4><i class="fas fa-calendar-alt"></i> تفاصيل الحادث</h4>
                                <div class="detail-item"><strong>التاريخ:</strong> ${this.formatDate(review.incidentDate + 'T' + review.incidentTime)}</div>
                                <div class="detail-item"><strong>المكان:</strong> ${review.incidentLocation}</div>
                                <div class="detail-item"><strong>نوع الحادث:</strong> ${this.getEventTypeLabel(review.incidentType)}</div>
                                <div class="detail-item"><strong>المسؤول عن الحادث:</strong> ${review.incidentResponsible}</div>
                            </div>

                            <div class="detail-section full-width">
                                <h4><i class="fas fa-exclamation-triangle"></i> وصف الحادث</h4>
                                <div class="detail-text">${review.incidentDescription}</div>
                            </div>

                            <div class="detail-section">
                                <h4><i class="fas fa-shield-alt"></i> تقييم الاستعداد والاستجابة</h4>
                                <div class="detail-item"><strong>استعداد الموظف:</strong> <span class="preparedness-badge ${review.employeePreparedness}">${this.getPreparednessLabel(review.employeePreparedness)}</span></div>
                                <div class="detail-item"><strong>جودة الاستجابة:</strong> <span class="response-badge ${review.responseQuality}">${this.getResponseLabel(review.responseQuality)}</span></div>
                            </div>

                            <div class="detail-section full-width">
                                <h4><i class="fas fa-search"></i> تحليل الحادث</h4>
                                <div class="detail-item"><strong>الأسباب الظاهرية:</strong></div>
                                <div class="detail-text">${review.apparentCauses}</div>
                                <div class="detail-item"><strong>الأسباب الجذرية:</strong></div>
                                <div class="detail-text">${review.rootCauses}</div>
                                <div class="detail-item"><strong>العوامل المساهمة:</strong></div>
                                <div class="detail-text">${review.contributingFactors}</div>
                            </div>

                            <div class="detail-section full-width">
                                <h4><i class="fas fa-undo"></i> خطط الاسترداد</h4>
                                <div class="detail-item"><strong>خطط الاسترداد المفعلة:</strong></div>
                                <div class="detail-text">${review.recoveryPlans}</div>
                                <div class="detail-item"><strong>مدى ملائمة الخطط:</strong></div>
                                <div class="detail-text">${review.recoveryEffectiveness}</div>
                            </div>

                            <div class="detail-section full-width">
                                <h4><i class="fas fa-tools"></i> الإجراءات التصحيحية</h4>
                                <div class="detail-text">${review.correctiveActions}</div>
                                <div class="detail-item"><strong>المسؤول عن التنفيذ:</strong> ${review.actionResponsible}</div>
                                <div class="detail-item"><strong>فترة التنفيذ:</strong> ${review.implementationPeriod}</div>
                            </div>

                            <div class="detail-section full-width">
                                <h4><i class="fas fa-eye"></i> آلية المتابعة</h4>
                                <div class="detail-text">${review.followupMechanism}</div>
                                <div class="detail-item"><strong>المسؤول عن المتابعة:</strong> ${review.followupResponsible}</div>
                                <div class="detail-item"><strong>توقيت المتابعة:</strong> ${review.followupTiming}</div>
                            </div>

                            <div class="detail-section full-width">
                                <h4><i class="fas fa-lightbulb"></i> الدروس المستفادة</h4>
                                <div class="detail-text">${review.lessonsLearned}</div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

        } catch (error) {
            console.error('Error viewing review details:', error);
            this.showNotification('خطأ في عرض تفاصيل المراجعة', 'error');
        }
    }

    // Delete review
    deleteReview(reviewId) {
        try {
            if (!confirm('هل أنت متأكد من حذف هذه المراجعة؟')) {
                return;
            }

            // Remove from array
            this.postIncidentReviews = this.postIncidentReviews.filter(r => r.id !== reviewId);

            // Save to localStorage
            localStorage.setItem('postIncidentReviews', JSON.stringify(this.postIncidentReviews));

            // Re-render reviews
            this.renderReviews();

            // Show success notification
            this.showNotification('تم حذف المراجعة بنجاح', 'success');

        } catch (error) {
            console.error('Error deleting review:', error);
            this.showNotification('خطأ في حذف المراجعة', 'error');
        }
    }
}

// Initialize the application
const securityManager = new SecurityEventsManager();
