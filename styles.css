/* CSS Variables for Theme Colors */
:root {
    /* Light Theme Colors */
    --primary-blue: #2563eb;
    --secondary-blue: #3b82f6;
    --light-blue: #dbeafe;
    --dark-blue: #1e40af;
    --primary-gray: #6b7280;
    --light-gray: #f3f4f6;
    --dark-gray: #374151;
    --white: #ffffff;
    --black: #111827;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #06b6d4;
    
    /* Background and Text */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --shadow: rgba(0, 0, 0, 0.1);
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --border-color: #475569;
    --shadow: rgba(0, 0, 0, 0.3);
    --light-gray: #334155;
    --dark-gray: #cbd5e1;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: all 0.3s ease;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo i {
    font-size: 2rem;
    color: var(--light-blue);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
}

.user-menu {
    position: relative;
    cursor: pointer;
}

.user-menu i {
    font-size: 1.2rem;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 20px var(--shadow);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.user-menu.active .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    color: var(--text-primary);
    text-align: right;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.dropdown-item:hover {
    background: var(--bg-secondary);
}

.dropdown-item:first-child {
    border-radius: 8px 8px 0 0;
}

.dropdown-item:last-child {
    border-radius: 0 0 8px 8px;
}

/* Navigation Styles */
.main-nav {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.nav-btn.active {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.nav-btn i {
    font-size: 1rem;
}

/* Content Sections */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Main Content */
.main-content {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
}

/* Section Styles */
.event-input-section,
.events-log-section {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px var(--shadow);
    border: 1px solid var(--border-color);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--light-blue);
}

.section-header h2 {
    color: var(--primary-blue);
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Form Styles */
.event-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-blue);
    color: white;
}

.btn-primary:hover {
    background: var(--dark-blue);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-secondary {
    background: var(--primary-gray);
    color: white;
}

.btn-secondary:hover {
    background: var(--dark-gray);
    transform: translateY(-2px);
}

.btn-danger {
    background: var(--danger);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-2px);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
    margin-top: 1rem;
}

/* Log Controls */
.log-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-input,
.filter-select {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.search-input {
    min-width: 250px;
}

/* Table Styles */
.events-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.events-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
}

.events-table th,
.events-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.events-table th {
    background: var(--light-blue);
    color: var(--primary-blue);
    font-weight: 600;
    position: sticky;
    top: 0;
}

.events-table tbody tr:hover {
    background: var(--bg-secondary);
}

/* Severity Badges */
.severity-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
}

.severity-low {
    background: #dcfce7;
    color: #166534;
}

.severity-medium {
    background: #fef3c7;
    color: #92400e;
}

.severity-high {
    background: #fed7d7;
    color: #c53030;
}

.severity-critical {
    background: #fecaca;
    color: #991b1b;
}

/* Dark theme severity badges */
[data-theme="dark"] .severity-low {
    background: #166534;
    color: #dcfce7;
}

[data-theme="dark"] .severity-medium {
    background: #92400e;
    color: #fef3c7;
}

[data-theme="dark"] .severity-high {
    background: #c53030;
    color: #fed7d7;
}

[data-theme="dark"] .severity-critical {
    background: #991b1b;
    color: #fecaca;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-open {
    background: #dbeafe;
    color: #1e40af;
}

.status-investigating {
    background: #fef3c7;
    color: #92400e;
}

.status-resolved {
    background: #dcfce7;
    color: #166534;
}

.status-closed {
    background: #f3f4f6;
    color: #6b7280;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: scale(1.1);
}

.view-btn {
    background: var(--info);
    color: white;
}

.edit-btn {
    background: var(--warning);
    color: white;
}

.delete-btn {
    background: var(--danger);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        padding: 0 1rem;
        flex-wrap: wrap;
    }

    .logo h1 {
        font-size: 1.2rem;
    }

    .main-nav {
        order: 3;
        width: 100%;
        margin-top: 1rem;
        justify-content: center;
    }

    .nav-btn {
        flex: 1;
        max-width: 120px;
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .main-content {
        padding: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .log-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-input {
        min-width: auto;
    }

    .events-table {
        font-size: 0.8rem;
    }

    .events-table th,
    .events-table td {
        padding: 0.5rem;
    }

    .form-actions {
        flex-direction: column;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--light-blue);
    border-radius: 12px 12px 0 0;
}

.modal-header h3 {
    color: var(--primary-blue);
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--primary-gray);
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: var(--danger);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Event Details Styles */
.event-detail {
    margin-bottom: 1rem;
}

.event-detail label {
    font-weight: 600;
    color: var(--text-secondary);
    display: block;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.event-detail .value {
    color: var(--text-primary);
    font-size: 1rem;
    padding: 0.5rem 0;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-gray);
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1100;
    animation: slideInRight 0.3s ease;
    max-width: 400px;
}

.notification.success {
    background: var(--success);
}

.notification.error {
    background: var(--danger);
}

.notification.warning {
    background: var(--warning);
}

.notification.info {
    background: var(--info);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Statistics Styles */
.stats-overview {
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.stat-card {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-blue);
}

.stat-card.critical::before {
    background: var(--danger);
}

.stat-card.high::before {
    background: var(--warning);
}

.stat-card.open::before {
    background: var(--info);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-blue);
    color: var(--primary-blue);
    font-size: 1.5rem;
}

.stat-card.critical .stat-icon {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.stat-card.high .stat-icon {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.stat-card.open .stat-icon {
    background: rgba(6, 182, 212, 0.1);
    color: var(--info);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.stat-content p {
    color: var(--text-secondary);
    margin: 0.25rem 0 0 0;
    font-size: 0.9rem;
}

/* Charts Styles */
.charts-section {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px var(--shadow);
    border: 1px solid var(--border-color);
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 1.5rem;
}

.chart-container {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.chart-header {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--light-blue);
}

.chart-header h3 {
    color: var(--primary-blue);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chart-content {
    position: relative;
    height: 300px;
}

.chart-content canvas {
    max-width: 100%;
    max-height: 100%;
}

/* Risk Analysis Styles */
.risk-analysis {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px var(--shadow);
    border: 1px solid var(--border-color);
}

.risk-score-container {
    margin-bottom: 2rem;
}

.risk-score-card {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.risk-score-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.risk-score-header h3 {
    color: var(--primary-blue);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.risk-score-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    justify-content: center;
}

.risk-gauge {
    width: 200px;
    height: 200px;
}

.risk-details {
    text-align: center;
}

.risk-level {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--warning);
}

.risk-score {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.risk-description {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Risk Categories */
.risk-categories {
    margin-bottom: 2rem;
}

.risk-category {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.risk-category:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px var(--shadow);
}

.risk-category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.risk-category-header h4 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.risk-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.risk-badge.low {
    background: #dcfce7;
    color: #166534;
}

.risk-badge.medium {
    background: #fef3c7;
    color: #92400e;
}

.risk-badge.high {
    background: #fed7d7;
    color: #c53030;
}

.risk-badge.critical {
    background: #fecaca;
    color: #991b1b;
}

[data-theme="dark"] .risk-badge.low {
    background: #166534;
    color: #dcfce7;
}

[data-theme="dark"] .risk-badge.medium {
    background: #92400e;
    color: #fef3c7;
}

[data-theme="dark"] .risk-badge.high {
    background: #c53030;
    color: #fed7d7;
}

[data-theme="dark"] .risk-badge.critical {
    background: #991b1b;
    color: #fecaca;
}

.risk-category-content p {
    color: var(--text-secondary);
    margin: 0.5rem 0 0 0;
    font-size: 0.9rem;
}

.risk-progress {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.risk-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--success), var(--warning), var(--danger));
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Recommendations */
.recommendations {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.recommendations-list {
    margin-top: 1rem;
}

.recommendation-item {
    background: var(--bg-primary);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--primary-blue);
    transition: all 0.3s ease;
}

.recommendation-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 10px var(--shadow);
}

.recommendation-item.high-priority {
    border-left-color: var(--danger);
}

.recommendation-item.medium-priority {
    border-left-color: var(--warning);
}

.recommendation-item.low-priority {
    border-left-color: var(--success);
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.recommendation-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

.recommendation-priority {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.recommendation-priority.high {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.recommendation-priority.medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.recommendation-priority.low {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.recommendation-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Login Page Styles */
.login-body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    position: relative;
    z-index: 2;
}

.login-card {
    background: var(--bg-primary);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 450px;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.login-logo i {
    font-size: 3rem;
    color: var(--primary-blue);
    background: var(--light-blue);
    padding: 1rem;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-logo h1 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.login-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: 1rem;
    color: var(--text-secondary);
    z-index: 1;
}

.input-group input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.password-toggle {
    position: absolute;
    left: 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: var(--primary-blue);
    background: rgba(37, 99, 235, 0.1);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.login-btn {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.login-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.login-error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--danger);
    color: var(--danger);
    padding: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.login-footer {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.demo-accounts h3 {
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: 1rem;
    text-align: center;
}

.demo-account {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.demo-account:hover {
    background: var(--light-blue);
    border-color: var(--primary-blue);
    transform: translateX(5px);
}

.demo-account strong {
    color: var(--primary-blue);
}

/* Login Background Animation */
.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
}

.security-icons {
    position: absolute;
    width: 100%;
    height: 100%;
}

.security-icons i {
    position: absolute;
    color: rgba(255, 255, 255, 0.1);
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
}

.security-icons i:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.security-icons i:nth-child(2) {
    top: 60%;
    left: 20%;
    animation-delay: 1s;
}

.security-icons i:nth-child(3) {
    top: 30%;
    right: 15%;
    animation-delay: 2s;
}

.security-icons i:nth-child(4) {
    bottom: 30%;
    right: 25%;
    animation-delay: 3s;
}

.security-icons i:nth-child(5) {
    bottom: 20%;
    left: 30%;
    animation-delay: 4s;
}

.security-icons i:nth-child(6) {
    top: 50%;
    right: 40%;
    animation-delay: 5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.3;
    }
}

/* Login Theme Toggle */
.login-theme-toggle {
    position: fixed;
    top: 2rem;
    left: 2rem;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.login-theme-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* User Management Styles */
.user-management {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px var(--shadow);
    border: 1px solid var(--border-color);
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.user-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.user-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow);
}

.user-card.inactive {
    opacity: 0.6;
    border-color: var(--primary-gray);
}

.user-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-blue);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
}

.user-info h4 {
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
    font-size: 1.1rem;
}

.user-info p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

.user-role {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.role-admin {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.role-analyst {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.role-operator {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.user-permissions {
    margin-bottom: 1rem;
}

.user-permissions h5 {
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.permission-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.permission-tag {
    background: var(--light-blue);
    color: var(--primary-blue);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.user-action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.edit-user-btn {
    background: var(--info);
    color: white;
}

.delete-user-btn {
    background: var(--danger);
    color: white;
}

.toggle-user-btn {
    background: var(--warning);
    color: white;
}

.user-action-btn:hover {
    transform: scale(1.1);
    opacity: 0.8;
}

/* User Form Styles */
.user-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 0.5rem;
}

.permission-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.permission-checkbox:hover {
    background: var(--bg-secondary);
}

.permission-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-blue);
}

.permission-checkbox span {
    font-size: 0.9rem;
    color: var(--text-primary);
}

/* Activity Log Styles */
.activity-log {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px var(--shadow);
    border: 1px solid var(--border-color);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: var(--bg-secondary);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-login {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.activity-logout {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.activity-user_management {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-blue);
}

.activity-event {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.activity-content {
    flex: 1;
}

.activity-description {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-meta {
    color: var(--text-secondary);
    font-size: 0.8rem;
    display: flex;
    gap: 1rem;
}

/* Responsive Updates */
@media (max-width: 768px) {
    .user-dropdown {
        right: -50px;
        min-width: 180px;
    }

    .permissions-grid {
        grid-template-columns: 1fr;
    }

    .users-grid {
        grid-template-columns: 1fr;
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .activity-meta {
        flex-direction: column;
        gap: 0.25rem;
    }
}

@media (max-width: 480px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .main-nav {
        order: -1;
        width: 100%;
        justify-content: center;
    }

    .nav-btn {
        flex: 1;
        justify-content: center;
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .nav-btn span {
        display: none;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .action-buttons {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-footer {
        flex-direction: column;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .risk-score-content {
        flex-direction: column;
        gap: 1rem;
    }
}
