<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة أمن المعلومات</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="login-body">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>نظام إدارة أمن المعلومات</h1>
                </div>
                <p class="login-subtitle">تسجيل الدخول للوصول إلى النظام</p>
            </div>

            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" required autocomplete="username" placeholder="أدخل اسم المستخدم">
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required autocomplete="current-password" placeholder="أدخل كلمة المرور">
                        <button type="button" class="password-toggle" id="passwordToggle">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>تسجيل الدخول</span>
                    <div class="loading-spinner" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>

                <div class="login-error" id="loginError" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="errorMessage">خطأ في اسم المستخدم أو كلمة المرور</span>
                </div>
            </form>

            <div class="login-footer">
                <div class="demo-accounts">
                    <h3>حسابات تجريبية:</h3>
                    <div class="demo-account" onclick="fillDemoAccount('admin', 'admin123')">
                        <strong>مدير النظام:</strong> admin / admin123
                    </div>
                    <div class="demo-account" onclick="fillDemoAccount('analyst', 'analyst123')">
                        <strong>محلل أمني:</strong> analyst / analyst123
                    </div>
                    <div class="demo-account" onclick="fillDemoAccount('operator', 'operator123')">
                        <strong>مشغل:</strong> operator / operator123
                    </div>
                </div>
            </div>
        </div>

        <div class="login-background">
            <div class="security-icons">
                <i class="fas fa-shield-alt"></i>
                <i class="fas fa-lock"></i>
                <i class="fas fa-user-shield"></i>
                <i class="fas fa-key"></i>
                <i class="fas fa-fingerprint"></i>
                <i class="fas fa-eye"></i>
            </div>
        </div>
    </div>

    <!-- Theme Toggle -->
    <button class="theme-toggle login-theme-toggle" id="themeToggle">
        <i class="fas fa-moon"></i>
    </button>

    <script src="auth.js"></script>
</body>
</html>
